// Account validation utilities for AccountSettings component

export interface ValidationError {
  field: string;
  message: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

// User data interface for validation
export interface UserDataForValidation {
  name?: string;
  email?: string;
  mobile?: string;
  phone?: string;
  gender?: string;
  dateOfBirth?: string;
  bio?: string;
  address?: {
    addressLine1?: string;
    addressLine2?: string;
    country?: string;
    state?: string;
    city?: string;
    postalCode?: string;
  };
  currencyCode?: string;
  language?: string;
}

/**
 * Validates a single field
 * @param field - Field name to validate
 * @param value - Value to validate
 * @returns Error message if invalid, null if valid
 */
export const validateField = (field: string, value: string | number): string | null => {
  const stringValue = String(value).trim();

  switch (field) {
    case 'name':
      if (!stringValue) return 'Full name is required';
      if (stringValue.length < 2) return 'Name must be at least 2 characters long';
      if (stringValue.length > 100) return 'Name must be less than 100 characters';
      if (!/^[a-zA-Z\s'-]+$/.test(stringValue)) return 'Name can only contain letters, spaces, hyphens, and apostrophes';
      break;

    case 'email':
      if (!stringValue) return 'Email address is required';
      if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(stringValue)) return 'Please enter a valid email address';
      if (stringValue.length > 254) return 'Email address is too long';
      break;

    case 'mobile':
    case 'phone':
      if (stringValue.length > 0) {
        const phoneRegex = /^\+?[1-9]\d{1,14}$/;
        const cleanPhone = stringValue.replace(/\D/g, '');

        if (cleanPhone.length < 8) return 'Phone number must be at least 8 digits';
        if (cleanPhone.length > 15) return 'Phone number must be less than 16 digits';
        if (!phoneRegex.test(stringValue.replace(/\s/g, ''))) return 'Please enter a valid phone number';
      }
      break;

    case 'bio':
      if (stringValue.length > 500) return 'Bio must be less than 500 characters';
      if (stringValue.length > 0 && stringValue.length < 10) return 'Bio must be at least 10 characters if provided';
      break;

    case 'address.addressLine1':
      if (stringValue.length > 0) {
        if (stringValue.length < 5) return 'Address line 1 must be at least 5 characters';
        if (stringValue.length > 100) return 'Address line 1 must be less than 100 characters';
      }
      break;

    case 'address.addressLine2':
      if (stringValue.length > 100) return 'Address line 2 must be less than 100 characters';
      break;

    case 'address.country':
      if (stringValue.length > 0) {
        if (stringValue.length < 2) return 'Country must be at least 2 characters';
        if (stringValue.length > 50) return 'Country must be less than 50 characters';
        if (!/^[a-zA-Z\s'-]+$/.test(stringValue)) return 'Country can only contain letters, spaces, hyphens, and apostrophes';
      }
      break;

    case 'address.state':
      if (stringValue.length > 0) {
        if (stringValue.length < 2) return 'State/Province must be at least 2 characters';
        if (stringValue.length > 50) return 'State/Province must be less than 50 characters';
        if (!/^[a-zA-Z\s'-]+$/.test(stringValue)) return 'State/Province can only contain letters, spaces, hyphens, and apostrophes';
      }
      break;

    case 'address.city':
      if (stringValue.length > 0) {
        if (stringValue.length < 2) return 'City must be at least 2 characters';
        if (stringValue.length > 50) return 'City must be less than 50 characters';
        if (!/^[a-zA-Z\s'-]+$/.test(stringValue)) return 'City can only contain letters, spaces, hyphens, and apostrophes';
      }
      break;

    case 'address.postalCode':
      if (stringValue.length > 0) {
        if (stringValue.length < 3) return 'Postal code must be at least 3 characters';
        if (stringValue.length > 10) return 'Postal code must be less than 10 characters';
        if (!/^[a-zA-Z0-9\s-]+$/.test(stringValue)) return 'Postal code can only contain letters, numbers, spaces, and hyphens';
      }
      break;

    case 'dateOfBirth':
      if (stringValue) {
        const birthDate = new Date(stringValue);
        const today = new Date();
        const age = today.getFullYear() - birthDate.getFullYear();
        const monthDiff = today.getMonth() - birthDate.getMonth();

        if (birthDate > today) return 'Date of birth cannot be in the future';
        if (age < 13 || (age === 13 && monthDiff < 0)) return 'You must be at least 13 years old';
        if (age > 120) return 'Please enter a valid date of birth';
      }
      break;
  }

  return null;
};

/**
 * Validates the entire user form data
 * @param userData - User data to validate
 * @param isProvider - Whether user is a provider (affects address validation)
 * @returns Validation result with errors
 */
export const validateUserData = (userData: UserDataForValidation, isProvider: boolean = false): ValidationResult => {
  const errors: ValidationError[] = [];

  // Validate required fields
  const nameError = validateField('name', userData.name || '');
  if (nameError) errors.push({ field: 'name', message: nameError });

  const emailError = validateField('email', userData.email || '');
  if (emailError) errors.push({ field: 'email', message: emailError });

  // Validate optional fields if they have values
  if (userData.mobile) {
    const mobileError = validateField('mobile', userData.mobile);
    if (mobileError) errors.push({ field: 'mobile', message: mobileError });
  }

  if (userData.phone) {
    const phoneError = validateField('phone', userData.phone);
    if (phoneError) errors.push({ field: 'phone', message: phoneError });
  }

  if (userData.bio) {
    const bioError = validateField('bio', userData.bio);
    if (bioError) errors.push({ field: 'bio', message: bioError });
  }

  if (userData.dateOfBirth) {
    const dobError = validateField('dateOfBirth', userData.dateOfBirth);
    if (dobError) errors.push({ field: 'dateOfBirth', message: dobError });
  }

  // Validate address fields (only for non-providers)
  if (!isProvider && userData.address) {
    const addressFields = [
      'addressLine1',
      'addressLine2',
      'country',
      'state',
      'city',
      'postalCode'
    ];

    addressFields.forEach(field => {
      const value = userData.address?.[field as keyof typeof userData.address];
      if (value) {
        const error = validateField(`address.${field}`, value);
        if (error) errors.push({ field: `address.${field}`, message: error });
      }
    });
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Sanitizes phone number input
 * @param input - Raw phone input
 * @returns Sanitized phone number
 */
export const sanitizePhoneNumber = (input: string): string => {
  const sanitized = input.startsWith('+')
    ? '+' + input.slice(1).replace(/\D/g, '')
    : input.replace(/\D/g, '');
  return sanitized.slice(0, 16);
};

/**
 * Formats validation errors for display
 * @param errors - Array of validation errors
 * @returns Formatted error messages
 */
export const formatValidationErrors = (errors: ValidationError[]): string => {
  if (errors.length === 0) return '';
  if (errors.length === 1) return errors[0].message;
  
  return `Multiple errors found:\n${errors.map(error => `• ${error.message}`).join('\n')}`;
};
