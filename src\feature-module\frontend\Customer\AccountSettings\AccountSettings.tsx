
import { <PERSON>a<PERSON>ser<PERSON><PERSON><PERSON>, FaExternalLinkAlt, FaCopy, FaEdit, FaSync, FaCheck, FaTimes } from 'react-icons/fa';
import {
  Select,
  SelectItem,
  DatePicker,
  Textarea,
  <PERSON>ert,
  <PERSON><PERSON>,
  Spinner,
  <PERSON>dal,
  <PERSON>dal<PERSON>nt,
  <PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON>,
  <PERSON>dal<PERSON>ooter,
  Tooltip,
  Progress,
  Divider,
} from '@heroui/react';

import CustomInput from '../../../components/CustomInput';
import BreadCrumb from '../../../components/common/breadcrumb/breadCrumb';
import CustomButton from '../../../components/CustomButton';
import { useAuth } from 'react-oidc-context';
import { useEffect, useState, useCallback, useRef } from 'react';
import { apiClient } from '../../../../api';
import { uploadToS3, deleteFromS3 } from '../aws/s3FileUpload';
import { ROLE } from '../../../../Role';
import moment from 'moment';

// Define user data interface
interface UserData {
  name?: string;
  username?: string;
  email?: string;
  phone?: string;
  mobile?: string;
  gender?: string;
  dateOfBirth?: string;
  bio?: string;
  address?: {
    addressLine1?: string;
    addressLine2?: string;
    country?: string;
    state?: string;
    city?: string;
    postalCode?: string;
  };
  currencyCode?: string;
  language?: string;
  profilePicture?: string;
}


const AccountSettings = () => {
  const auth = useAuth();
  const [userData, setUserData] = useState<UserData>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [uploadLoading, setUploadLoading] = useState(false);
  const [removeLoading, setRemoveLoading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Role-based address editing states
  const [isProvider, setIsProvider] = useState(false);
  const [showProviderPortalAlert, setShowProviderPortalAlert] = useState(false);

  // Profile image states
  const [showImagePreview, setShowImagePreview] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [showImageUrl, setShowImageUrl] = useState(false);
  const [urlCopied, setUrlCopied] = useState(false);

  // Edit modal states
  const [showEditModal, setShowEditModal] = useState(false);
  const [editFormData, setEditFormData] = useState<UserData>({});
  const [editFormErrors, setEditFormErrors] = useState<Record<string, string>>({});
  const [editSaveLoading, setEditSaveLoading] = useState(false);
  const [refreshLoading, setRefreshLoading] = useState(false);

  // Get user ID from auth context - using preferred_username as primary identifier
  // Fall back to 'sub' or email if preferred_username is not available
  const preferredUsername = auth.user?.profile?.preferred_username;
  const sub = auth.user?.profile?.sub;
  const email = auth.user?.profile?.email;

  // Use the first available identifier
  const uid = preferredUsername || sub || email;
  console.log("ggg")
  // Note: Token refresh functionality has been disabled as requested



  // Check user role and set appropriate state
  useEffect(() => {
    if (auth.user) {
      console.log('User authenticated:', auth.user.profile);
      console.log('User ID (preferred_username):', uid);

      // Log all available profile fields to help identify user information
      console.log('All profile fields:', Object.keys(auth.user.profile));
      console.log('Profile data:', auth.user.profile);

      // Check user roles from cognito groups
      const roles = auth.user?.profile['cognito:groups'] as string[] | undefined;
      const hasProviderRole = roles?.includes(ROLE.PROVIDER);

      console.log('Has provider role:', hasProviderRole);

      setIsProvider(hasProviderRole || false);

      // Address fields are disabled for all users
      // No verification needed as fields are not editable

      if (!uid) {
        console.warn('preferred_username is not available in the user profile!');
        console.log('Checking alternative user identifiers...');
        console.log('sub:', auth.user.profile.sub);
        console.log('email:', auth.user.profile.email);
      }
    } else {
      console.log('No authenticated user found');
    }
  }, [auth.user, uid]);

  // Function to fetch user data - wrapped in useCallback to prevent unnecessary re-renders
  const fetchUserData = useCallback(async () => {
    if (!uid) {
      setLoading(false);
      setError("User ID not found. Please log in again.");
      return;
    }

    try {
      setLoading(true);
      console.log(`Fetching user data for ID (preferred_username): ${uid}`);
      console.log(`API endpoint: http://localhost:3010/api/v1/user/${uid}`);

      // Check if user is authenticated
      if (!auth.isAuthenticated) {
        console.error('User is not authenticated');
        setError("You are not logged in. Please log in to view your account settings.");
        setLoading(false);
        return;
      }

      // Get the ID token from auth context
      const token = auth.user?.id_token;

      if (!token) {
        console.error('No ID token available');
        setError("Authentication token not found. Please log in again.");
        setLoading(false);
        return;
      }

      console.log('Using ID token for authentication');

      // Make API request with authentication token
      console.log('Final API endpoint:', `/api/v1/user/${uid}`);

      const response = await apiClient.get(`/api/v1/user/${uid}`)
      console.log("User data response:",response)
   
      if (response.status === 200 && response.data) {
        console.log('User data fetched successfully:', response.data);
        console.log('User data fields:', Object.keys(response.data));

        // Check if we need to adapt the field names from the API response
        const apiData = response.data.user || response.data;
        console.log('Raw API data:', apiData);
        const adaptedData: UserData = {};

        // Map API fields to our UserData interface
        // This handles potential differences in field naming between API and our form
        if (apiData) {
          // Try different possible field names for each property
          adaptedData.name = apiData.name || apiData.fullName || apiData.displayName || '';
          adaptedData.email = apiData.email || '';
          adaptedData.mobile = apiData.mobile || apiData.phone || apiData.phoneNumber || '';
          adaptedData.gender = apiData.gender || apiData.sex || '';
          adaptedData.dateOfBirth = apiData.dateOfBirth || apiData.dob || '';
          adaptedData.bio = apiData.bio || apiData.description || '';
          adaptedData.language = apiData.language || apiData.preferredLanguage || '';
          adaptedData.currencyCode = apiData.currencyCode || apiData.currency || '';
          adaptedData.profilePicture = apiData.profilePicture || apiData.profileImage || apiData.avatar || apiData.picture || '';

          // Handle address object
          if (apiData.address && typeof apiData.address === 'object') {
            adaptedData.address = {
              addressLine1: apiData.address.addressLine1 || apiData.address.street || apiData.address.line1 || '',
              addressLine2: apiData.address.addressLine2 || apiData.address.line2 || '',
              country: apiData.address.country || '',
              state: apiData.address.state || apiData.address.province || apiData.address.region || '',
              city: apiData.address.city || '',
              postalCode: apiData.address.postalCode || apiData.address.zipCode || apiData.address.zip || ''
            };
          } else {
            adaptedData.address = {
              addressLine1: '',
              addressLine2: '',
              country: '',
              state: '',
              city: '',
              postalCode: ''
            };
          }
        }

        console.log('Adapted user data:', adaptedData);

        console.log('Adapted user data:', adaptedData);

        // If no data is found, add some fallback data for testing
        if (!adaptedData.name && !adaptedData.email) {
          console.log('No user data found, using fallback data for testing');
          adaptedData.name = auth.user?.profile?.name || auth.user?.profile?.given_name || 'Test User';
          adaptedData.email = auth.user?.profile?.email || '<EMAIL>';
          adaptedData.mobile = '+1234567890';
          adaptedData.gender = 'prefer-not-to-say';
          adaptedData.bio = 'This is a sample bio for testing purposes.';
          adaptedData.currencyCode = 'USD';
          adaptedData.language = 'English';
        }

        // Set the adapted user data in state
        setUserData(adaptedData);
        setError(null);

        // Show success message if data was loaded
        if (Object.values(adaptedData).some(value => value && value !== '')) {
          setSuccess("User data loaded successfully!");
          // Clear success message after 3 seconds
          setTimeout(() => {
            setSuccess(null);
          }, 3000);
        }

        // Log each field for debugging
        console.log('Name:', adaptedData.name);
        console.log('Username:', adaptedData.username);
        console.log('Email:', adaptedData.email);
        console.log('Phone:', adaptedData.phone);
        console.log('Gender:', adaptedData.gender);
        console.log('Date of Birth:', adaptedData.dateOfBirth);
      } else {
        console.error('Failed to fetch user data:', response);
        setError("Failed to fetch user data. Please try again later.");
      }
    } catch (err: unknown) {
      console.error('Error fetching user data:', err);

      // More detailed error logging
      if (err && typeof err === 'object') {
        const error = err as Record<string, unknown>;
        if (error.response && typeof error.response === 'object') {
          const response = error.response as Record<string, unknown>;
          console.error('Error response:', response.status, response.data);
        } else if (error.request) {
          console.error('No response received:', error.request);
        } else if (error.message && typeof error.message === 'string') {
          console.error('Error setting up request:', error.message);
        }
      }

      setError("An error occurred while fetching your account data.");
    } finally {
      setLoading(false);
    }
  }, [uid, auth.user?.id_token, auth.isAuthenticated, auth.user?.profile?.name, auth.user?.profile?.given_name, auth.user?.profile?.email]);

  // Call fetchUserData when component mounts
  useEffect(() => {
    if (uid) {
      fetchUserData();
    } else {
      console.warn('No user ID available to fetch data');
    }
  }, [uid, fetchUserData]);











  // Refresh user data function
  const handleRefreshUserData = async () => {
    if (!uid) {
      setError("User ID not found. Please log in again.");
      return;
    }

    try {
      setRefreshLoading(true);
      setError(null);
      setSuccess(null);

      console.log('Refreshing user data for UID:', uid);

      // Fetch fresh user data from the API with extended timeout
      const response = await apiClient.get(`/api/v1/user/${uid}`, {
        timeout: 30000, // 30 seconds for data refresh
      });

      if (response.status === 200 && response.data) {
        const apiData = response.data;
        console.log('Fresh user data received:', apiData);

        // Adapt the API response to match our UserData interface
        const adaptedData: UserData = {
          name: apiData.name || apiData.fullName || apiData.displayName || '',
          username: apiData.username || apiData.userName || '',
          email: apiData.email || '',
          phone: apiData.phone || apiData.phoneNumber || '',
          mobile: apiData.mobile || apiData.mobileNumber || '',
          gender: apiData.gender || apiData.sex || '',
          dateOfBirth: apiData.dateOfBirth || apiData.dob || '',
          bio: apiData.bio || apiData.description || '',
          language: apiData.language || apiData.preferredLanguage || '',
          currencyCode: apiData.currencyCode || apiData.currency || '',
          profilePicture: apiData.profilePicture || apiData.profileImage || apiData.avatar || apiData.picture || '',
        };

        // Handle address object
        if (apiData.address && typeof apiData.address === 'object') {
          adaptedData.address = {
            addressLine1: apiData.address.addressLine1 || apiData.address.street || apiData.address.line1 || '',
            addressLine2: apiData.address.addressLine2 || apiData.address.line2 || '',
            country: apiData.address.country || '',
            state: apiData.address.state || apiData.address.province || apiData.address.region || '',
            city: apiData.address.city || '',
            postalCode: apiData.address.postalCode || apiData.address.zipCode || apiData.address.zip || ''
          };
        } else {
          adaptedData.address = {
            addressLine1: '',
            addressLine2: '',
            country: '',
            state: '',
            city: '',
            postalCode: ''
          };
        }

        setUserData(adaptedData);
        setSuccess("User data refreshed successfully!");

        // Auto-hide success message after 3 seconds
        setTimeout(() => setSuccess(null), 3000);
      } else {
        throw new Error('Invalid response from server');
      }
    } catch (error: unknown) {
      console.error('Error refreshing user data:', error);

      // Provide specific error messages based on error type
      let errorMessage = "Failed to refresh user data. Please try again.";

      if (error && typeof error === 'object' && 'code' in error) {
        const axiosError = error as { code?: string; message?: string };

        if (axiosError.code === 'ECONNABORTED' || axiosError.message?.includes('timeout')) {
          errorMessage = "Request timed out while refreshing data. Please check your internet connection and try again.";
        } else if (axiosError.code === 'ECONNREFUSED' || axiosError.code === 'ERR_NETWORK') {
          errorMessage = "Cannot connect to the server. Please check if the backend service is running and try again.";
        }
      }

      setError(errorMessage);
    } finally {
      setRefreshLoading(false);
    }
  };

  // Handle file selection for profile picture upload
  const handleFileSelect = () => {
    fileInputRef.current?.click();
  };

  // Handle file upload
  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    const validTypes = ['image/jpeg', 'image/png', 'image/jpg'];
    if (!validTypes.includes(file.type)) {
      setError("Invalid file type. Please upload a .jpg or .png file.");
      return;
    }

    // Validate file size (500KB = 512000 bytes)
    if (file.size > 512000) {
      setError("File is too large. Maximum size is 500KB.");
      return;
    }

    try {
      setUploadLoading(true);
      setError(null);
      setSuccess(null); // Clear any previous success messages
      setUploadProgress(10); // Start progress

      // Create a real progress tracker
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 5; // Slower progress to be more realistic
        });
      }, 200);

      console.log("Starting file upload to S3...", {
        fileName: file.name,
        fileSize: file.size,
        fileType: file.type
      });

      // Upload file to S3
      const imageUrl = await uploadToS3(file, 'profile-images');

      if (!imageUrl) {
        throw new Error("Failed to get image URL after upload");
      }

      console.log("S3 upload successful, received URL:", imageUrl);

      // Clear interval and set progress to 100%
      clearInterval(progressInterval);
      setUploadProgress(100);

      // Update user data with new profile image URL
      const updatedUserData = {
        ...userData,
        profilePicture: imageUrl
      };

      setUserData(updatedUserData);
      console.log("Updated user data with new profile image:", updatedUserData);

      // Save changes to backend
      console.log("Saving updated profile image to database...");
      const savedData = await saveUserData(updatedUserData);

      if (!savedData) {
        throw new Error("Failed to save profile image to database");
      }

      console.log("Profile image saved to database successfully:", savedData);

      // Show success message and image URL
      setSuccess("Profile picture uploaded and saved successfully!");
      setShowImageUrl(true);

      // Dispatch custom event to notify sidebar of profile picture update
      window.dispatchEvent(new CustomEvent('profilePictureUpdated', {
        detail: {
          profileImage: imageUrl,
          userId: uid
        }
      }));

      // Auto-hide URL after 10 seconds
      setTimeout(() => {
        setShowImageUrl(false);
      }, 10000);

    } catch (err: unknown) {
      console.error("Error uploading profile picture:", err);
      setUploadProgress(0);

      // More detailed error logging
      if (err && typeof err === 'object') {
        const error = err as Record<string, unknown>;
        if (error.response && typeof error.response === 'object') {
          const response = error.response as Record<string, unknown>;
          console.error('Error response:', response.status, response.data);
        } else if (error.request) {
          console.error('No response received:', error.request);
        } else if (error.message && typeof error.message === 'string') {
          console.error('Error setting up request:', error.message);
        }
      }

      // Provide specific error messages based on error type
      let errorMessage = "Failed to upload profile picture. Please try again.";

      if (err && typeof err === 'object' && 'message' in err) {
        const errorObj = err as { message: string; code?: string };

        if (errorObj.message.includes('timeout') || errorObj.code === 'ECONNABORTED') {
          errorMessage = "Upload timed out. The server is taking too long to respond. Please check your internet connection and try again.";
        } else if (errorObj.message.includes('Failed to save user data')) {
          errorMessage = "Image uploaded successfully, but failed to save to your profile. Please try refreshing the page.";
        } else if (errorObj.message.includes('ECONNREFUSED') || errorObj.message.includes('ERR_NETWORK')) {
          errorMessage = "Cannot connect to the server. Please check if the backend service is running and try again.";
        } else {
          errorMessage = errorObj.message;
        }
      }

      setError(errorMessage);
    } finally {
      setUploadLoading(false);
      // Clear the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }

      // Reset progress after a delay
      setTimeout(() => {
        setUploadProgress(0);
      }, 2000);
    }
  };

  // Copy image URL to clipboard
  const copyImageUrlToClipboard = () => {
    if (userData.profilePicture) {
      navigator.clipboard.writeText(userData.profilePicture)
        .then(() => {
          setUrlCopied(true);
          setTimeout(() => setUrlCopied(false), 2000);
        })
        .catch(err => {
          console.error('Failed to copy URL: ', err);
        });
    }
  };

  // Helper function to save user data to backend
  const saveUserData = async (dataToSave: UserData) => {
    if (!uid) {
      throw new Error("User ID not found. Please log in again.");
    }

    // Check if user is authenticated
    if (!auth.isAuthenticated) {
      throw new Error("You are not logged in. Please log in to save your account settings.");
    }

    // Get the ID token from auth context
    const token = auth.user?.id_token;

    if (!token) {
      throw new Error("Authentication token not found. Please log in again.");
    }

    try {
      console.log('Using ID token for authentication when saving data');
      console.log('Final save API endpoint:', `/api/v1/user/${uid}`);
      console.log('Data being saved:', dataToSave);
      console.log('Date of birth value:', dataToSave.dateOfBirth);

      // Validate required fields
      if (!dataToSave.name || !dataToSave.email) {
        throw new Error('Name and email are required fields');
      }

      // Prepare data for MongoDB by ensuring all fields are properly formatted
      const formattedData = {
        ...dataToSave,
        // Convert date strings to ISO format if they exist and are valid
        dateOfBirth: dataToSave.dateOfBirth && moment(dataToSave.dateOfBirth).isValid()
          ? moment(dataToSave.dateOfBirth).format('YYYY-MM-DD')
          : dataToSave.dateOfBirth || null,

        // Ensure profilePicture is a string
        profilePicture: dataToSave.profilePicture || '',

        // Clean up any undefined values
        name: dataToSave.name || '',
        email: dataToSave.email || '',
        mobile: dataToSave.mobile || '',
        phone: dataToSave.phone || '',
        gender: dataToSave.gender || '',
        bio: dataToSave.bio || '',
        language: dataToSave.language || '',
        currencyCode: dataToSave.currencyCode || '',

        // Handle address object properly
        address: dataToSave.address ? {
          addressLine1: dataToSave.address.addressLine1 || '',
          addressLine2: dataToSave.address.addressLine2 || '',
          country: dataToSave.address.country || '',
          state: dataToSave.address.state || '',
          city: dataToSave.address.city || '',
          postalCode: dataToSave.address.postalCode || ''
        } : {}
      };

      // Remove any undefined or null values at the top level
      Object.keys(formattedData).forEach(key => {
        if ((formattedData as Record<string, unknown>)[key] === undefined) {
          delete (formattedData as Record<string, unknown>)[key];
        }
      });

      console.log('Formatted data for MongoDB:', formattedData);
      console.log('Request headers will include:', {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${auth.user?.id_token ? '[TOKEN_PRESENT]' : '[NO_TOKEN]'}`
      });

      // Make the API request with extended timeout using relative URL
      const response = await apiClient.put(`/api/v1/user/${uid}`, formattedData, {
        timeout: 45000, // 45 seconds for save operations
        headers: {
          'Content-Type': 'application/json',
        }
      });

      console.log('API request completed successfully:', {
        status: response.status,
        statusText: response.statusText,
        headers: response.headers
      });

      // Check for successful response
      if (response.status !== 200) {
        console.error('Unexpected response status:', response.status);
        throw new Error(`Failed to update user data. Server returned status ${response.status}`);
      }

      if (!response.data) {
        console.error('No data returned from server');
        throw new Error("Failed to update user data. No data returned from server.");
      }

      console.log('User data updated successfully:', response.data);

      // Return the data from the response
      return response.data;
    } catch (error: unknown) {
      console.error('Error in saveUserData:', error);

      // Handle specific error types with better user messages
      if (error && typeof error === 'object' && 'code' in error) {
        const axiosError = error as {
          code?: string;
          message?: string;
          response?: {
            status?: number;
            data?: {
              message?: string;
              error?: string;
              details?: unknown;
            }
          }
        };

        // Log detailed error information for debugging
        console.error('Axios error details:', {
          code: axiosError.code,
          message: axiosError.message,
          status: axiosError.response?.status,
          responseData: axiosError.response?.data
        });

        if (axiosError.code === 'ECONNABORTED' || axiosError.message?.includes('timeout')) {
          throw new Error("Request timed out. The server is taking too long to respond. Please check your internet connection and try again.");
        }

        if (axiosError.code === 'ECONNREFUSED' || axiosError.code === 'ERR_NETWORK') {
          throw new Error("Cannot connect to the server. Please check if the backend service is running and try again.");
        }

        if (axiosError.response?.status === 500) {
          const serverMessage = axiosError.response?.data?.message || axiosError.response?.data?.error;
          throw new Error(`Server error (500): ${serverMessage || 'The server is experiencing issues. Please try again later.'}`);
        }

        if (axiosError.response?.status && axiosError.response.status >= 400 && axiosError.response.status < 500) {
          const clientMessage = axiosError.response?.data?.message || axiosError.response?.data?.error;
          throw new Error(`Request error (${axiosError.response.status}): ${clientMessage || 'Invalid request data'}`);
        }
      }

      // Rethrow with more specific error message
      if (error instanceof Error) {
        throw new Error(`Failed to save user data: ${error.message}`);
      } else {
        throw new Error("Failed to save user data due to an unknown error");
      }
    }
  };

  // Handle profile picture removal
  const handleRemoveProfilePicture = async () => {
    // Check if there's a profile image to remove
    if (!userData.profilePicture) {
      setError("No profile picture to remove.");
      return;
    }

    try {
      setRemoveLoading(true);
      setError(null);
      setSuccess(null); // Clear any previous success messages

      console.log("Starting profile picture removal process...");
      console.log("Image URL to delete:", userData.profilePicture);

      // Delete file from S3
      const deleteResult = await deleteFromS3(userData.profilePicture);
      console.log("S3 deletion result:", deleteResult);

      // Update user data to remove profile image URL
      const updatedUserData = {
        ...userData,
        profilePicture: ''
      };

      setUserData(updatedUserData);
      console.log("Updated user data after image removal:", updatedUserData);

      // Save changes to backend
      console.log("Saving updated user data without profile image ");
      const savedData = await saveUserData(updatedUserData);

      if (!savedData) {
        throw new Error("Failed to update database after removing profile image");
      }

      console.log("Database updated successfully after image removal:", savedData);

      // Show success message
      setSuccess("Profile picture removed successfully !");

      // Dispatch custom event to notify sidebar of profile picture removal
      window.dispatchEvent(new CustomEvent('profilePictureUpdated', {
        detail: {
          profileImage: '',
          userId: uid
        }
      }));

      // Hide image URL display if it was showing
      setShowImageUrl(false);
    } catch (err: unknown) {
      console.error("Error removing profile picture:", err);

      // More detailed error logging
      if (err && typeof err === 'object') {
        const error = err as Record<string, unknown>;
        if (error.response && typeof error.response === 'object') {
          const response = error.response as Record<string, unknown>;
          console.error('Error response:', response.status, response.data);
        } else if (error.request) {
          console.error('No response received:', error.request);
        } else if (error.message && typeof error.message === 'string') {
          console.error('Error setting up request:', error.message);
        }
      }

      setError(err instanceof Error ? err.message : "Failed to remove profile picture. Please try again.");
    } finally {
      setRemoveLoading(false);
    }
  };

  // Edit modal functions
  const handleOpenEditModal = () => {
    // Pre-populate edit form with current user data
    setEditFormData({ ...userData });
    setEditFormErrors({});
    setShowEditModal(true);
  };

  const handleCloseEditModal = () => {
    setShowEditModal(false);
    setEditFormData({});
    setEditFormErrors({});
    setEditSaveLoading(false);
  };

  // Real-time field validation function
  const validateField = (field: string, value: string | number): string | null => {
    const stringValue = String(value).trim();

    switch (field) {
      case 'name':
        if (!stringValue) return 'Full name is required';
        if (stringValue.length < 2) return 'Name must be at least 2 characters long';
        if (stringValue.length > 100) return 'Name must be less than 100 characters';
        if (!/^[a-zA-Z\s'-]+$/.test(stringValue)) return 'Name can only contain letters, spaces, hyphens, and apostrophes';
        break;

      case 'email':
        if (!stringValue) return 'Email address is required';
        if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(stringValue)) return 'Please enter a valid email address';
        if (stringValue.length > 254) return 'Email address is too long';
        break;

      case 'mobile':
        if (stringValue.length > 0) {
          const phoneRegex = /^\+?[1-9]\d{1,14}$/;
          const cleanPhone = stringValue.replace(/\D/g, '');

          if (cleanPhone.length < 8) return 'Phone number must be at least 8 digits';
          if (cleanPhone.length > 15) return 'Phone number must be less than 16 digits';
          if (!phoneRegex.test(stringValue.replace(/\s/g, ''))) return 'Please enter a valid phone number';
        }
        break;

      case 'bio':
        if (stringValue.length > 500) return 'Bio must be less than 500 characters';
        if (stringValue.length > 0 && stringValue.length < 10) return 'Bio must be at least 10 characters if provided';
        break;

      case 'address.addressLine1':
        if (stringValue.length > 0) {
          if (stringValue.length < 5) return 'Address line 1 must be at least 5 characters';
          if (stringValue.length > 100) return 'Address line 1 must be less than 100 characters';
        }
        break;

      case 'address.addressLine2':
        if (stringValue.length > 100) return 'Address line 2 must be less than 100 characters';
        break;

      case 'address.country':
        if (stringValue.length > 0) {
          if (stringValue.length < 2) return 'Country must be at least 2 characters';
          if (stringValue.length > 50) return 'Country must be less than 50 characters';
          if (!/^[a-zA-Z\s'-]+$/.test(stringValue)) return 'Country can only contain letters, spaces, hyphens, and apostrophes';
        }
        break;

      case 'address.state':
        if (stringValue.length > 0) {
          if (stringValue.length < 2) return 'State/Province must be at least 2 characters';
          if (stringValue.length > 50) return 'State/Province must be less than 50 characters';
          if (!/^[a-zA-Z\s'-]+$/.test(stringValue)) return 'State/Province can only contain letters, spaces, hyphens, and apostrophes';
        }
        break;

      case 'address.city':
        if (stringValue.length > 0) {
          if (stringValue.length < 2) return 'City must be at least 2 characters';
          if (stringValue.length > 50) return 'City must be less than 50 characters';
          if (!/^[a-zA-Z\s'-]+$/.test(stringValue)) return 'City can only contain letters, spaces, hyphens, and apostrophes';
        }
        break;

      case 'address.postalCode':
        if (stringValue.length > 0) {
          if (stringValue.length < 3) return 'Postal code must be at least 3 characters';
          if (stringValue.length > 10) return 'Postal code must be less than 10 characters';
          if (!/^[a-zA-Z0-9\s-]+$/.test(stringValue)) return 'Postal code can only contain letters, numbers, spaces, and hyphens';
        }
        break;
    }

    return null;
  };

  const handleEditFormChange = (field: string, value: string | number) => {
    // Disallow editing email
    if (field === 'email') return;

    // Handle nested fields (e.g., address.city)
    setEditFormData((prevData) => {
      const updatedData = { ...prevData };
      const keys = field.split('.');
      let current: Record<string, unknown> = updatedData as Record<string, unknown>;

      for (let i = 0; i < keys.length - 1; i++) {
        const key = keys[i];
        if (!current[key]) current[key] = {};
        current = current[key] as Record<string, unknown>;
      }

      const lastKey = keys[keys.length - 1];

      if (lastKey === 'mobile' || lastKey === 'phone') {
        const input = String(value);
        const sanitized = input.startsWith('+')
          ? '+' + input.slice(1).replace(/\D/g, '')
          : input.replace(/\D/g, '');
        current[lastKey] = sanitized.slice(0, 16);
      } else {
        current[lastKey] = value;
      }

      return updatedData;
    });

    // Perform real-time validation with a small delay to avoid excessive validation
    setTimeout(() => {
      const error = validateField(field, value);
      setEditFormErrors(prev => {
        const newErrors = { ...prev };
        if (error) {
          newErrors[field] = error;
        } else {
          delete newErrors[field];
        }
        return newErrors;
      });
    }, 300); // 300ms delay for better UX
  };

  const validateEditForm = () => {
    const errors: Record<string, string> = {};

    // Validate Name
    if (!editFormData.name?.trim()) {
      errors.name = 'Full name is required';
    } else if (editFormData.name.trim().length < 2) {
      errors.name = 'Name must be at least 2 characters long';
    } else if (editFormData.name.trim().length > 100) {
      errors.name = 'Name must be less than 100 characters';
    } else if (!/^[a-zA-Z\s'-]+$/.test(editFormData.name.trim())) {
      errors.name = 'Name can only contain letters, spaces, hyphens, and apostrophes';
    }

    // Validate Email
    if (!editFormData.email?.trim()) {
      errors.email = 'Email address is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(editFormData.email.trim())) {
      errors.email = 'Please enter a valid email address';
    } else if (editFormData.email.trim().length > 254) {
      errors.email = 'Email address is too long';
    }

    // Validate Phone Number
    if (editFormData.mobile && editFormData.mobile.trim().length > 0) {
      const phoneRegex = /^\+?[1-9]\d{1,14}$/;
      const cleanPhone = editFormData.mobile.replace(/\D/g, '');

      if (cleanPhone.length < 8) {
        errors.mobile = 'Phone number must be at least 8 digits';
      } else if (cleanPhone.length > 15) {
        errors.mobile = 'Phone number must be less than 16 digits';
      } else if (!phoneRegex.test(editFormData.mobile.replace(/\s/g, ''))) {
        errors.mobile = 'Please enter a valid phone number';
      }
    }

    // Validate Bio
    if (editFormData.bio) {
      if (editFormData.bio.length > 500) {
        errors.bio = 'Bio must be less than 500 characters';
      } else if (editFormData.bio.trim().length > 0 && editFormData.bio.trim().length < 10) {
        errors.bio = 'Bio must be at least 10 characters if provided';
      }
    }

    // Validate Date of Birth
    if (editFormData.dateOfBirth) {
      const birthDate = new Date(editFormData.dateOfBirth);
      const today = new Date();
      const age = today.getFullYear() - birthDate.getFullYear();
      const monthDiff = today.getMonth() - birthDate.getMonth();

      if (birthDate > today) {
        errors.dateOfBirth = 'Date of birth cannot be in the future';
      } else if (age < 13 || (age === 13 && monthDiff < 0)) {
        errors.dateOfBirth = 'You must be at least 13 years old';
      } else if (age > 120) {
        errors.dateOfBirth = 'Please enter a valid date of birth';
      }
    }

    // Validate Address fields (only for non-providers)
    if (!isProvider) {
      if (editFormData.address?.addressLine1?.trim()) {
        if (editFormData.address.addressLine1.trim().length < 5) {
          errors['address.addressLine1'] = 'Address line 1 must be at least 5 characters';
        } else if (editFormData.address.addressLine1.trim().length > 100) {
          errors['address.addressLine1'] = 'Address line 1 must be less than 100 characters';
        }
      }

      if (editFormData.address?.addressLine2?.trim()) {
        if (editFormData.address.addressLine2.trim().length > 100) {
          errors['address.addressLine2'] = 'Address line 2 must be less than 100 characters';
        }
      }

      if (editFormData.address?.country?.trim()) {
        if (editFormData.address.country.trim().length < 2) {
          errors['address.country'] = 'Country must be at least 2 characters';
        } else if (editFormData.address.country.trim().length > 50) {
          errors['address.country'] = 'Country must be less than 50 characters';
        } else if (!/^[a-zA-Z\s'-]+$/.test(editFormData.address.country.trim())) {
          errors['address.country'] = 'Country can only contain letters, spaces, hyphens, and apostrophes';
        }
      }

      if (editFormData.address?.state?.trim()) {
        if (editFormData.address.state.trim().length < 2) {
          errors['address.state'] = 'State/Province must be at least 2 characters';
        } else if (editFormData.address.state.trim().length > 50) {
          errors['address.state'] = 'State/Province must be less than 50 characters';
        } else if (!/^[a-zA-Z\s'-]+$/.test(editFormData.address.state.trim())) {
          errors['address.state'] = 'State/Province can only contain letters, spaces, hyphens, and apostrophes';
        }
      }

      if (editFormData.address?.city?.trim()) {
        if (editFormData.address.city.trim().length < 2) {
          errors['address.city'] = 'City must be at least 2 characters';
        } else if (editFormData.address.city.trim().length > 50) {
          errors['address.city'] = 'City must be less than 50 characters';
        } else if (!/^[a-zA-Z\s'-]+$/.test(editFormData.address.city.trim())) {
          errors['address.city'] = 'City can only contain letters, spaces, hyphens, and apostrophes';
        }
      }

      if (editFormData.address?.postalCode?.trim()) {
        const postalCode = editFormData.address.postalCode.trim();
        if (postalCode.length < 3) {
          errors['address.postalCode'] = 'Postal code must be at least 3 characters';
        } else if (postalCode.length > 10) {
          errors['address.postalCode'] = 'Postal code must be less than 10 characters';
        } else if (!/^[a-zA-Z0-9\s-]+$/.test(postalCode)) {
          errors['address.postalCode'] = 'Postal code can only contain letters, numbers, spaces, and hyphens';
        }
      }
    }

    setEditFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSaveEditForm = async () => {
    if (!validateEditForm()) {
      return;
    }

    try {
      setEditSaveLoading(true);
      setError(null);

      console.log('Starting save process with form data:', editFormData);
      console.log('User ID:', uid);
      console.log('Auth status:', auth.isAuthenticated);
      console.log('API base URL:', import.meta.env.VITE_APP_BACKEND_PORT);

      // Save the edited data
      const updatedData = await saveUserData(editFormData);

      // Update the main user data state
      setUserData(updatedData);

      // Show success message
      setSuccess("Your account information has been updated successfully!");

      // Close the modal
      handleCloseEditModal();

      // Clear success message after 5 seconds
      setTimeout(() => {
        setSuccess(null);
      }, 5000);
    } catch (err: unknown) {
      console.error('Error updating user data:', err);

      // More detailed error logging
      if (err instanceof Error) {
        console.error('Error message:', err.message);
        console.error('Error stack:', err.stack);
      }

      setError(err instanceof Error ? err.message : "An error occurred while updating your account data.");
    } finally {
      setEditSaveLoading(false);
    }
  };

  // Alert content
  // const alertTitle = userData.email ? 'Account Verified' : 'Verify Your Account';
  // const description = userData.email
  //   ? 'Your account is verified and active.'
  //   : 'Please verify your account to access all features.';

  if (loading) {
    return (
      <div className="p-6 min-h-screen flex items-center justify-center">
        <Spinner size="lg" color="primary" label="Loading account data..." />
      </div>
    );
  }

  // Debug log to see what userData contains
  console.log('Rendering AccountSettings with userData:', userData);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 p-4">
      <div className="max-w-6xl mx-auto">
        {/* Compact Header */}
        <div className="flex items-center justify-between mb-4 bg-white rounded-lg shadow-sm border border-gray-100 p-4">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full flex items-center justify-center">
              <FaUserCircle className="text-white text-lg" />
            </div>
            <div>
              <h1 className="text-lg font-bold text-gray-800">
                Hey {userData.name?.split(' ')[0] || 'Friend'}! 👋
              </h1>
              <div className="flex items-center gap-2 text-xs text-gray-600">
                <div className="w-1.5 h-1.5 rounded-full bg-green-500"></div>
                <span>{auth.user?.profile?.email}</span>
              </div>
            </div>
          </div>
          <div className="flex gap-2">
            <CustomButton
              color="primary"
              label="✏️ Edit"
              size="sm"
              variant="shadow"
              startContent={<FaEdit />}
              onPress={handleOpenEditModal}
              className="font-medium text-xs"
            />
            <CustomButton
              color="default"
              label={refreshLoading ? "🔄" : "🔄"}
              size="sm"
              variant="bordered"
              startContent={<FaSync className={refreshLoading ? "animate-spin" : ""} />}
              onPress={handleRefreshUserData}
              isLoading={refreshLoading}
              isDisabled={refreshLoading}
              className="text-xs"
            />
          </div>
        </div>

        {/* Compact Alert Messages */}
        <div className="space-y-2 mb-4">
          {success && (
            <Alert
              description={`🎉 ${success}`}
              title="Great!"
              variant="faded"
              radius="md"
              color="success"
              onClose={() => setSuccess(null)}
              className="text-sm"
            />
          )}

          {error && (
            <Alert
              description={`😔 ${error}`}
              title="Oops!"
              variant="faded"
              radius="md"
              color="danger"
              onClose={() => setError(null)}
              className="text-sm"
            />
          )}

          {uploadProgress > 0 && uploadProgress < 100 && (
            <Alert
              description={`📤 Uploading... ${uploadProgress}%`}
              title="Almost there!"
              variant="faded"
              radius="md"
              color="primary"
              className="text-sm"
            />
          )}

          {editSaveLoading && (
            <Alert
              description="💾 Saving changes..."
              title="Just a sec!"
              variant="faded"
              radius="md"
              color="primary"
              className="text-sm"
            />
          )}

          {showProviderPortalAlert && isProvider && (
            <Alert
              description="🏢 Address updates available in Provider Portal"
              title="Provider Info"
              variant="faded"
              radius="md"
              color="primary"
              onClose={() => setShowProviderPortalAlert(false)}
              endContent={
                <Button
                  color="primary"
                  size="sm"
                  variant="flat"
                  onPress={() => window.location.href = "/provider-portal"}
                  className="text-xs"
                >
                  Go →
                </Button>
              }
              className="text-sm"
            />
          )}
        </div>

        {/* Compact All-in-One Layout */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">

          {/* Profile & Photo Column */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-4">
            <h3 className="text-sm font-semibold text-gray-800 mb-3 flex items-center gap-2">
              📸 Profile
            </h3>

            {/* Compact Profile Picture */}
            <div className="text-center mb-4">
              <div
                className="w-16 h-16 bg-gradient-to-br from-blue-100 to-indigo-100 rounded-full flex items-center justify-center overflow-hidden cursor-pointer hover:shadow-md transition-all mx-auto mb-2 border-2 border-white shadow-sm"
                onClick={() => userData.profilePicture && setShowImagePreview(true)}
              >
                {userData.profilePicture ? (
                  <img
                    src={userData.profilePicture}
                    alt="Profile"
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <FaUserCircle className="text-gray-400 text-2xl" />
                )}
              </div>

              <div className="flex gap-1 justify-center">
                <input
                  type="file"
                  ref={fileInputRef}
                  className="hidden"
                  accept=".jpg,.jpeg,.png"
                  onChange={handleFileUpload}
                />
                <CustomButton
                  color="primary"
                  label={uploadLoading ? "📤" : "📤"}
                  size="sm"
                  variant="flat"
                  isDisabled={uploadLoading || removeLoading}
                  onPress={handleFileSelect}
                  className="text-xs min-w-8"
                />
                {userData.profilePicture && (
                  <CustomButton
                    color="danger"
                    label="🗑️"
                    size="sm"
                    variant="flat"
                    isDisabled={uploadLoading || removeLoading}
                    onPress={handleRemoveProfilePicture}
                    className="text-xs min-w-8"
                  />
                )}
              </div>

              {uploadProgress > 0 && (
                <Progress
                  value={uploadProgress}
                  color={uploadProgress === 100 ? "success" : "primary"}
                  size="sm"
                  className="mt-2"
                />
              )}

              <div className="text-xs text-gray-500 mt-2">
                Max 500KB • 320×320px
                {userData.profilePicture && (
                  <div className="text-green-600 mt-1">
                    <FaCheck className="inline mr-1" />
                    ✨ Perfect!
                  </div>
                )}
              </div>
            </div>

            {/* Profile Status */}
            <div className="text-center">
              {userData.name && userData.email ? (
                <div className="bg-green-50 text-green-700 px-2 py-1 rounded-full text-xs font-medium">
                  <FaCheck className="inline mr-1" />
                  Complete Profile
                </div>
              ) : (
                <div className="bg-orange-50 text-orange-700 px-2 py-1 rounded-full text-xs font-medium">
                  <FaTimes className="inline mr-1" />
                  Needs Info
                </div>
              )}
            </div>
          </div>

          {/* Personal Info Column */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-4">
            <h3 className="text-sm font-semibold text-gray-800 mb-3 flex items-center gap-2">
              👤 Personal Info
            </h3>

            <div className="space-y-3">
              <div>
                <label className="text-xs font-medium text-gray-500">🏷️ Name</label>
                <p className="text-sm text-gray-900 font-medium">
                  {userData.name || <span className="text-gray-400 italic">Add name</span>}
                </p>
              </div>

              <div>
                <label className="text-xs font-medium text-gray-500">📧 Email</label>
                <p className="text-sm text-gray-900 font-medium">
                  {userData.email || <span className="text-gray-400 italic">No email</span>}
                </p>
              </div>

              <div>
                <label className="text-xs font-medium text-gray-500">📱 Phone</label>
                <p className="text-sm text-gray-900 font-medium">
                  {userData.mobile || <span className="text-gray-400 italic">Add phone</span>}
                </p>
              </div>

              <div className="grid grid-cols-2 gap-2">
                <div>
                  <label className="text-xs font-medium text-gray-500">👥 Gender</label>
                  <p className="text-xs text-gray-900 font-medium capitalize">
                    {userData.gender || <span className="text-gray-400 italic">-</span>}
                  </p>
                </div>

                <div>
                  <label className="text-xs font-medium text-gray-500">🎂 Birthday</label>
                  <p className="text-xs text-gray-900 font-medium">
                    {userData.dateOfBirth ?
                      moment(userData.dateOfBirth).format('MMM DD') :
                      <span className="text-gray-400 italic">-</span>
                    }
                  </p>
                </div>
              </div>

              {userData.bio && (
                <div>
                  <label className="text-xs font-medium text-gray-500">✍️ Bio</label>
                  <p className="text-xs text-gray-900 leading-relaxed line-clamp-3">
                    {userData.bio}
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Location & Preferences Column */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-4">
            <h3 className="text-sm font-semibold text-gray-800 mb-3 flex items-center gap-2">
              🏠 Location & Settings
              {isProvider && (
                <span className="text-xs bg-blue-100 text-blue-700 px-2 py-0.5 rounded-full">Provider</span>
              )}
            </h3>

            <div className="space-y-3">
              <div>
                <label className="text-xs font-medium text-gray-500">📍 Address</label>
                <p className="text-sm text-gray-900 font-medium">
                  {userData.address?.addressLine1 || <span className="text-gray-400 italic">Add address</span>}
                </p>
                {userData.address?.city && userData.address?.state && (
                  <p className="text-xs text-gray-600">
                    {userData.address.city}, {userData.address.state}
                  </p>
                )}
              </div>

              <div className="grid grid-cols-2 gap-2">
                <div>
                  <label className="text-xs font-medium text-gray-500">🌍 Country</label>
                  <p className="text-xs text-gray-900 font-medium">
                    {userData.address?.country || <span className="text-gray-400 italic">-</span>}
                  </p>
                </div>

                <div>
                  <label className="text-xs font-medium text-gray-500">📮 ZIP</label>
                  <p className="text-xs text-gray-900 font-medium">
                    {userData.address?.postalCode || <span className="text-gray-400 italic">-</span>}
                  </p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-2">
                <div>
                  <label className="text-xs font-medium text-gray-500">🌐 Language</label>
                  <p className="text-xs text-gray-900 font-medium">
                    {userData.language || <span className="text-gray-400 italic">English</span>}
                  </p>
                </div>

                <div>
                  <label className="text-xs font-medium text-gray-500">💰 Currency</label>
                  <p className="text-xs text-gray-900 font-medium">
                    {userData.currencyCode || <span className="text-gray-400 italic">USD</span>}
                  </p>
                </div>
              </div>

              {isProvider && (
                <div className="bg-blue-50 rounded-md p-2 border border-blue-200">
                  <p className="text-xs text-blue-700 mb-1">
                    🏢 Provider account - manage address in Provider Portal
                  </p>
                  <Button
                    color="primary"
                    size="sm"
                    variant="flat"
                    onPress={() => window.location.href = "/provider-portal"}
                    className="text-xs h-6"
                  >
                    Go to Portal →
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Image Preview Modal */}
        {showImagePreview && userData.profilePicture && (
          <Modal
            isOpen={showImagePreview}
            onClose={() => setShowImagePreview(false)}
            size="3xl"
          >
            <ModalContent>
              <ModalHeader className="flex flex-col gap-1">
                Profile Image Preview
              </ModalHeader>
              <ModalBody>
                {userData.profilePicture && (
                  <div className="flex justify-center">
                    <img
                      src={userData.profilePicture}
                      alt="Profile Preview"
                      className="max-w-full max-h-[70vh] object-contain"
                    />
                  </div>
                )}
                <div className="mt-2 text-sm text-gray-600 break-all">
                  {userData.profilePicture}
                </div>
              </ModalBody>
              <ModalFooter>
                <Button
                  color="primary"
                  onPress={() => setShowImagePreview(false)}
                >
                  Close
                </Button>
              </ModalFooter>
            </ModalContent>
          </Modal>
        )}

        {/* Edit Account Modal */}
        <Modal
          isOpen={showEditModal}
          onClose={handleCloseEditModal}
          size="4xl"
          scrollBehavior="inside"
          classNames={{
            base: "max-h-[95vh]",
            body: "py-6",
            header: "border-b border-gray-100 bg-gradient-to-r from-blue-50 to-indigo-50",
            footer: "border-t border-gray-100 bg-gray-50"
          }}
        >
          <ModalContent>
            <ModalHeader className="flex flex-col gap-1">
              <div className="flex items-center gap-2">
                <FaEdit className="text-blue-600" />
                <h2 className="text-xl font-bold text-gray-800">Edit Account Information</h2>
              </div>
              <p className="text-sm text-gray-600 font-normal">Update your personal information and preferences</p>
              {/* Validation Status Indicator */}
              <div className="flex items-center gap-2 mt-2">
                {Object.keys(editFormErrors).length === 0 ? (
                  <div className="flex items-center gap-1 text-green-600">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-xs font-medium">All fields valid</span>
                  </div>
                ) : (
                  <div className="flex items-center gap-1 text-red-600">
                    <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                    <span className="text-xs font-medium">
                      {Object.keys(editFormErrors).length} error{Object.keys(editFormErrors).length > 1 ? 's' : ''} found
                    </span>
                  </div>
                )}
              </div>
            </ModalHeader>
            <ModalBody>
              <div className="space-y-6">
                {/* General Information Section */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-700 mb-4 flex items-center gap-2">
                    <FaUserCircle className="text-blue-600" />
                    General Information
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <CustomInput
                        label="Full Name"
                        isRequired
                        placeholder="Enter your full name"
                        type="text"
                        value={editFormData.name || ''}
                        onValueChange={(value) => handleEditFormChange('name', value)}
                        variant="bordered"
                        isInvalid={!!editFormErrors.name}
                        errorMessage={editFormErrors.name}
                      />
                    </div>
                    <div>
                      <CustomInput
                        label="Email Address"
                        isRequired
                        placeholder="Enter your email"
                        type="email"
                        value={editFormData.email || ''}
                        isDisabled={true}
                        description="Email cannot be edited"
                        variant="bordered"
                      />
                    </div>
                    <div>
                      <CustomInput
                        label="Phone Number"
                        placeholder="+94XXXXXXXXX"
                        type="tel"
                        value={editFormData.mobile || ''}
                        onValueChange={(value) => handleEditFormChange('mobile', value)}
                        variant="bordered"
                        isInvalid={!!editFormErrors.mobile}
                        errorMessage={editFormErrors.mobile}
                      />
                    </div>
                    <div>
                      <Select
                        label="Gender"
                        selectedKeys={editFormData.gender ? [editFormData.gender] : []}
                        onChange={(e) => {
                          const value = e.target.value;
                          handleEditFormChange('gender', value);

                          // Clear any gender validation errors
                          setEditFormErrors(prev => {
                            const newErrors = { ...prev };
                            delete newErrors.gender;
                            return newErrors;
                          });
                        }}
                        variant="bordered"
                        placeholder="Select your gender"
                        isInvalid={!!editFormErrors.gender}
                        errorMessage={editFormErrors.gender}
                      >
                        <SelectItem key="male">Male</SelectItem>
                        <SelectItem key="female">Female</SelectItem>
                        <SelectItem key="other">Other</SelectItem>
                        <SelectItem key="prefer-not-to-say">Prefer not to say</SelectItem>
                      </Select>
                    </div>
                    <div className="md:col-span-2">
                      <DatePicker
                        label="Date of Birth"
                        showMonthAndYearPickers
                        variant="bordered"
                        onChange={(date) => {
                          if (date) {
                            const dateString = date.toString();
                            handleEditFormChange('dateOfBirth', dateString);

                            // Validate date immediately
                            const birthDate = new Date(dateString);
                            const today = new Date();
                            const age = today.getFullYear() - birthDate.getFullYear();
                            const monthDiff = today.getMonth() - birthDate.getMonth();

                            let error = null;
                            if (birthDate > today) {
                              error = 'Date of birth cannot be in the future';
                            } else if (age < 13 || (age === 13 && monthDiff < 0)) {
                              error = 'You must be at least 13 years old';
                            } else if (age > 120) {
                              error = 'Please enter a valid date of birth';
                            }

                            setEditFormErrors(prev => {
                              const newErrors = { ...prev };
                              if (error) {
                                newErrors.dateOfBirth = error;
                              } else {
                                delete newErrors.dateOfBirth;
                              }
                              return newErrors;
                            });
                          }
                        }}
                        isInvalid={!!editFormErrors.dateOfBirth}
                        errorMessage={editFormErrors.dateOfBirth}
                        description="You must be at least 13 years old"
                      />
                    </div>
                    <div className="md:col-span-2">
                      <Textarea
                        label="Bio"
                        placeholder="Tell us about yourself"
                        variant="bordered"
                        value={editFormData.bio || ''}
                        onValueChange={(value) => handleEditFormChange('bio', value)}
                        isInvalid={!!editFormErrors.bio}
                        errorMessage={editFormErrors.bio}
                        maxLength={500}
                        description={`${editFormData.bio?.length || 0}/500 characters`}
                      />
                    </div>
                  </div>
                </div>

                {/* Address Section - Only show for customers */}
                {!isProvider && (
                  <div>
                    <Divider className="my-4" />
                    <h3 className="text-lg font-semibold text-gray-700 mb-4">Address Information</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="md:col-span-2">
                        <CustomInput
                          label="Address Line 1"
                          isRequired
                          placeholder="Enter address line 1"
                          type="text"
                          value={editFormData.address?.addressLine1 || ''}
                          onValueChange={(value) => handleEditFormChange('address.addressLine1', value)}
                          variant="bordered"
                          isInvalid={!!editFormErrors['address.addressLine1']}
                          errorMessage={editFormErrors['address.addressLine1']}
                        />
                      </div>
                      <div className="md:col-span-2">
                        <CustomInput
                          label="Address Line 2"
                          placeholder="Enter address line 2 (optional)"
                          type="text"
                          value={editFormData.address?.addressLine2 || ''}
                          onValueChange={(value) => handleEditFormChange('address.addressLine2', value)}
                          variant="bordered"
                          isInvalid={!!editFormErrors['address.addressLine2']}
                          errorMessage={editFormErrors['address.addressLine2']}
                        />
                      </div>
                      <div>
                        <CustomInput
                          label="Country"
                          isRequired
                          placeholder="Enter your country"
                          type="text"
                          value={editFormData.address?.country || ''}
                          onValueChange={(value) => handleEditFormChange('address.country', value)}
                          variant="bordered"
                          isInvalid={!!editFormErrors['address.country']}
                          errorMessage={editFormErrors['address.country']}
                        />
                      </div>
                      <div>
                        <CustomInput
                          label="State/Province"
                          isRequired
                          placeholder="Enter your state"
                          type="text"
                          value={editFormData.address?.state || ''}
                          onValueChange={(value) => handleEditFormChange('address.state', value)}
                          variant="bordered"
                          isInvalid={!!editFormErrors['address.state']}
                          errorMessage={editFormErrors['address.state']}
                        />
                      </div>
                      <div>
                        <CustomInput
                          label="City"
                          isRequired
                          placeholder="Enter your city"
                          type="text"
                          value={editFormData.address?.city || ''}
                          onValueChange={(value) => handleEditFormChange('address.city', value)}
                          variant="bordered"
                          isInvalid={!!editFormErrors['address.city']}
                          errorMessage={editFormErrors['address.city']}
                        />
                      </div>
                      <div>
                        <CustomInput
                          label="Postal Code"
                          isRequired
                          placeholder="Enter postal code"
                          type="text"
                          value={editFormData.address?.postalCode || ''}
                          onValueChange={(value) => handleEditFormChange('address.postalCode', value)}
                          variant="bordered"
                          isInvalid={!!editFormErrors['address.postalCode']}
                          errorMessage={editFormErrors['address.postalCode']}
                        />
                      </div>
                    </div>
                  </div>
                )}

                {/* Preferences Section */}
                <div>
                  <Divider className="my-4" />
                  <h3 className="text-lg font-semibold text-gray-700 mb-4">Preferences</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Select
                        label="Currency"
                        selectedKeys={editFormData.currencyCode ? [editFormData.currencyCode] : []}
                        onChange={(e) => handleEditFormChange('currencyCode', e.target.value)}
                        variant="bordered"
                        placeholder="Choose your currency"
                      >
                        <SelectItem key="USD">USD</SelectItem>
                        <SelectItem key="EUR">EUR</SelectItem>
                        <SelectItem key="GBP">GBP</SelectItem>
                        <SelectItem key="INR">INR</SelectItem>
                        <SelectItem key="AUD">AUD</SelectItem>
                        <SelectItem key="CAD">CAD</SelectItem>
                        <SelectItem key="JPY">JPY</SelectItem>
                      </Select>
                    </div>
                    <div>
                      <Select
                        label="Language"
                        selectedKeys={editFormData.language ? [editFormData.language] : []}
                        onChange={(e) => handleEditFormChange('language', e.target.value)}
                        variant="bordered"
                        placeholder="Choose your language"
                      >
                        <SelectItem key="English">English</SelectItem>
                        <SelectItem key="Spanish">Spanish</SelectItem>
                        <SelectItem key="French">French</SelectItem>
                        <SelectItem key="German">German</SelectItem>
                        <SelectItem key="Chinese">Chinese</SelectItem>
                        <SelectItem key="Hindi">Hindi</SelectItem>
                      </Select>
                    </div>
                  </div>
                </div>
              </div>
            </ModalBody>
            <ModalFooter>
              <div className="flex flex-col gap-3 w-full">
                {/* Validation Summary */}
                {Object.keys(editFormErrors).length > 0 && (
                  <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                    <div className="flex items-center gap-2 mb-2">
                      <div className="w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
                        <span className="text-white text-xs font-bold">!</span>
                      </div>
                      <span className="text-sm font-semibold text-red-800">
                        Please fix the following errors:
                      </span>
                    </div>
                    <ul className="text-xs text-red-700 space-y-1 ml-6">
                      {Object.entries(editFormErrors).map(([field, error]) => (
                        <li key={field} className="list-disc">
                          <span className="font-medium">
                            {field.includes('.')
                              ? field.split('.').map(part => part.charAt(0).toUpperCase() + part.slice(1)).join(' ')
                              : field.charAt(0).toUpperCase() + field.slice(1)
                            }:
                          </span> {error}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                {/* Action Buttons */}
                <div className="flex gap-3 w-full justify-end">
                  <CustomButton
                    color="default"
                    variant="bordered"
                    label="Cancel"
                    onPress={handleCloseEditModal}
                    isDisabled={editSaveLoading}
                  />
                  <CustomButton
                    color="primary"
                    label={editSaveLoading ? "Saving..." : "Save Changes"}
                    onPress={handleSaveEditForm}
                    isLoading={editSaveLoading}
                    isDisabled={editSaveLoading || Object.keys(editFormErrors).length > 0}
                    startContent={!editSaveLoading ? <FaEdit /> : undefined}
                  />
                </div>
              </div>
            </ModalFooter>
          </ModalContent>
        </Modal>
      </div>
    </div>
  );
};

export default AccountSettings;
